from transformers import BitsAndBytesConfig
import torch

model_variant = "4b-it" # 27b-text-it
model_id = f"google/medgemma-{model_variant}"

use_quantization = True

model_kwargs = dict(
    torch_dtype=torch.bfloat16,
    device_map="auto",
)

if use_quantization:
    model_kwargs["quantization_config"] = BitsAndBytesConfig(load_in_4bit=True)

import os
from PIL import Image
from IPython.display import Image as IPImage, display, Markdown

# Image attribution: Stillwaterising, CC0, via Wikimedia Commons
image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"  # @param {type: "string"}
! wget -nc -q {image_url}

image_filename = r"C:\Users\<USER>\Documents\Puneet\Chest_Xray_PA_3-8-2010.png"
image = Image.open(image_filename)

system_instruction = "You are an expert radiologist."
prompt = "Describe this X-ray"

messages = [
    {
        "role": "system",
        "content": [{"type": "text", "text": system_instruction}]
    },
    {
        "role": "user",
        "content": [
            {"type": "text", "text": prompt},
            {"type": "image", "image": image}
        ]
    }
]

import transformers
print(transformers.__version__)

from transformers import pipeline

pipe = pipeline(
    "image-text-to-text",
    model=model_id,
    model_kwargs=model_kwargs,
    use_fast=True
)

pipe.model.generation_config.do_sample = False

output = pipe(text=messages, max_new_tokens=300)
response = output[0]["generated_text"][-1]["content"]

display(Markdown(f"\n\n---\n\n**[ User ]**\n\n{prompt}"))
display(IPImage(filename=image_filename, width=512, height=512))
display(Markdown(f"\n\n---\n\n**[ MedGemma ]**\n\n{response}\n\n---\n\n"))

system_instruction = "You are a specialized AI assistant for healthcare campaign keyword analysis."

patient_data_json_string = """
{
    "Results": [],
    "Problems": [
      {
        "ProblemName": "Dementia",
        "Type": "Family History",
        "DetailText": "[Mother]"
      }
    ],
    "Medications": [
      {
        "MedType": "Rx",
        "MedicationName": "Gabapentin 300 MG Oral Capsule",
        "DoseQuantity": ""
      },
      {
        "MedType": "Rx",
        "MedicationName": "Pramipexole Dihydrochloride 0.5 MG Oral Tablet",
        "DoseQuantity": ""
      },
      {
        "MedType": "Rx",
        "MedicationName": "traZODone HCl 50 MG Oral Tablet",
        "DoseQuantity": ""
      },
      {
        "MedType": "Rx",
        "MedicationName": "Naproxen 500 MG Oral Tablet",
        "DoseQuantity": ""
      },
      {
        "MedType": "Rx",
        "MedicationName": "Fexofenadine HCl 180 MG Oral Tablet",
        "DoseQuantity": ""
      },
      {
        "MedType": "Rx",
        "MedicationName": "Pramipexole Dihydrochloride 0.125 MG Oral Tablet",
        "DoseQuantity": ""
      }
    ],
    "Allergies": []
  }
"""

campaign_keywords_json_string = """
[
  "A1C",
  "ACE Inhibitors",
  "Acetaminophen",
  "Acute Coronary Syndrome",
  "Acute Heart Failure",
  "Acute Kidney Injury",
  "Acute Myocardial Infarction",
  "Acute Respiratory Distress Syndrome",
  "Acute Stroke",
  "Acute Worsening of COPD"
]
"""

prompt = f"""
**AI Agent Task: Relevant Campaign Keyword Selection based on Patient Data**

**Objective:**
You are a specialized AI assistant. Your task is to analyze structured patient data (in JSON format) and, based on this analysis, select the **top 7 most relevant keywords** from a provided list of `campaign_keywords`. The selected keywords must strictly reflect the patient's key medical information, including conditions, significant findings, treatments, and risk factors as indicated in their data.

**Input Data:**
1.  `patient_data_json_string`: A JSON object representing a single patient's record. This may contain sections like "Results" (lab/vital signs), "Problems" (medical/surgical/family history), "Medications", and "Allergies".
2.  `campaign_keywords_json_string`: A JSON array of pre-defined campaign keywords. Your selection must come **exclusively** from this list.

**Note on Missing Patient Data Sections:**
If sections (e.g., Results, Problems, Allergies) are missing from the `patient_data_json_string`, base your relevance assessment only on the sections that are present.

**Strict Relevance Criteria:**
- **Only** select keywords that are given in `campaign_keywords_json_string`.
- Do **not infer** or assume medical conditions that are not explicitly or strongly implied.
- If **none** of the campaign keywords are relevant to the patient data, return:None
  ```json
  

**Data for Analysis:**
Patient Data:
{patient_data_json_string}

Campaign Keywords (Candidate List):
{campaign_keywords_json_string}
Based on the instructions, the patient_data, and the campaign_keywords provided above, generate the JSON object as described. Your response must be ONLY a valid JSON object with the two fields described, with no extra text, markdown, or explanation.
Give me relevant keywords that are present in `campaign_keywords_json_string`, if none of them are relevant, then return none
Give me relevant keywords that are present in `campaign_keywords_json_string`, if none of them are relevant, then return none
"""

messages = [
    {
        "role": "system",
        "content": [{"type": "text", "text": system_instruction}]
    },
    {
        "role": "user",
        "content": [
            {"type": "text", "text": prompt},
            {"type": "image", "image": image}
        ]
    }
]

output = pipe(text=messages, max_new_tokens=1000)
response = output[0]["generated_text"][-1]["content"]

display(Markdown(f"\n\n---\n\n**[ User ]**\n\n{prompt}"))
display(Markdown(f"\n\n---\n\n**[ MedGemma ]**\n\n{response}\n\n---\n\n"))

