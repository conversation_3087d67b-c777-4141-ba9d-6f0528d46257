import os
import io
import base64
import traceback
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager
import uvicorn
import paddle
from fastapi import FastAPI, HTTPException, File, UploadFile, Form
from pydantic import BaseModel
from PIL import Image
import numpy as np
from paddleocr import PaddleOCR

# Global OCR model
ocr_model = None

@asynccontextmanager
async def lifespan(_: FastAPI):
    """Manage application lifespan events"""
    # Startup
    global ocr_model

    try:
        print("Setting up GPU device...")
        # Set GPU device
        paddle.set_device('gpu')
        print(f"GPU available: {paddle.is_compiled_with_cuda()}")

        print("Loading PaddleOCR model...")
        # Initialize PaddleOCR with working configuration (exactly matching test_paddle.py)
        ocr_model = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )
        print("PaddleOCR model loaded successfully!")

    except Exception as e:
        print(f"Error loading PaddleOCR model: {str(e)}")
        print(traceback.format_exc())
        raise e

    yield

    # Shutdown
    print("Shutting down PaddleOCR API...")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="PaddleOCR API",
    description="Fast OCR inference API using PaddleOCR with GPU support",
    version="1.0.0",
    lifespan=lifespan
)

# Pydantic models for request/response
class OCRResult(BaseModel):
    text: str
    confidence: float
    bbox: List[List[float]]  # Bounding box coordinates [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

class OCRResponse(BaseModel):
    results: List[OCRResult]
    total_detections: int
    processing_time_ms: float
    image_size: Dict[str, int]  # {"width": w, "height": h}

class OCRConfig(BaseModel):
    text_score: Optional[float] = 0.5  # Confidence threshold for filtering results
    use_dilation: Optional[bool] = False  # Text dilation (not used in current implementation)



@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "PaddleOCR API is running",
        "status": "healthy",
        "model_loaded": ocr_model is not None,
        "gpu_available": paddle.is_compiled_with_cuda(),
        "paddle_device": str(paddle.get_device())
    }

@app.post("/ocr", response_model=OCRResponse)
async def extract_text(
    file: UploadFile = File(...),
    config: str = Form(default='{"text_score": 0.5}')
):
    """
    Extract text from an uploaded image with confidence scores and bounding boxes
    
    Args:
        file: Image file (PNG, JPG, JPEG, etc.)
        config: JSON string with OCR configuration options
    
    Returns:
        OCRResponse with extracted text, confidence scores, and bounding boxes
    """
    if ocr_model is None:
        raise HTTPException(status_code=503, detail="OCR model not loaded")
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        import time
        start_time = time.time()
        
        # Read and process the image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        image_size = {"width": image.width, "height": image.height}
        
        # Convert PIL image to numpy array for PaddleOCR
        image_np = np.array(image)
        
        # Parse configuration
        try:
            import json
            ocr_config = json.loads(config)
        except json.JSONDecodeError:
            ocr_config = {"text_score": 0.5}
        
        # Run OCR inference
        results = ocr_model.predict(image_np)

        # Process results - PaddleOCR predict returns a list with OCRResult objects
        ocr_results = []

        if results and isinstance(results, list) and len(results) > 0:
            # Get the first result (OCRResult object)
            ocr_result = results[0]

            # Check if it has the expected keys
            if 'rec_texts' in ocr_result and 'rec_scores' in ocr_result and 'rec_polys' in ocr_result:
                texts = ocr_result['rec_texts']
                scores = ocr_result['rec_scores']
                polys = ocr_result['rec_polys']

                for text, score, poly in zip(texts, scores, polys):
                    if score >= ocr_config.get("text_score", 0.5):
                        # Convert poly to the expected bbox format
                        bbox = [[float(point[0]), float(point[1])] for point in poly]
                        ocr_results.append(OCRResult(
                            text=text.strip(),
                            confidence=float(score),
                            bbox=bbox
                        ))
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        return OCRResponse(
            results=ocr_results,
            total_detections=len(ocr_results),
            processing_time_ms=round(processing_time, 2),
            image_size=image_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

@app.post("/ocr-base64", response_model=OCRResponse)
async def extract_text_base64(
    image_base64: str = Form(...),
    config: str = Form(default='{"text_score": 0.5}')
):
    """
    Extract text from a base64-encoded image
    
    Args:
        image_base64: Base64-encoded image string
        config: JSON string with OCR configuration options
    
    Returns:
        OCRResponse with extracted text, confidence scores, and bounding boxes
    """
    if ocr_model is None:
        raise HTTPException(status_code=503, detail="OCR model not loaded")
    
    try:
        # Decode base64 image
        if image_base64.startswith('data:image'):
            # Remove data URL prefix if present
            image_base64 = image_base64.split(',')[1]
        
        image_data = base64.b64decode(image_base64)
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        image_size = {"width": image.width, "height": image.height}
        
        # Convert PIL image to numpy array for PaddleOCR
        image_np = np.array(image)
        
        # Parse configuration
        try:
            import json
            ocr_config = json.loads(config)
        except json.JSONDecodeError:
            ocr_config = {"text_score": 0.5}
        
        import time
        start_time = time.time()
        
        # Run OCR inference
        results = ocr_model.predict(image_np)

        # Process results - same logic as file upload endpoint
        ocr_results = []

        if results and isinstance(results, list) and len(results) > 0:
            # Get the first result (OCRResult object)
            ocr_result = results[0]

            # Check if it has the expected keys
            if 'rec_texts' in ocr_result and 'rec_scores' in ocr_result and 'rec_polys' in ocr_result:
                texts = ocr_result['rec_texts']
                scores = ocr_result['rec_scores']
                polys = ocr_result['rec_polys']

                for text, score, poly in zip(texts, scores, polys):
                    if score >= ocr_config.get("text_score", 0.5):
                        # Convert poly to the expected bbox format
                        bbox = [[float(point[0]), float(point[1])] for point in poly]
                        ocr_results.append(OCRResult(
                            text=text.strip(),
                            confidence=float(score),
                            bbox=bbox
                        ))
        
        processing_time = (time.time() - start_time) * 1000
        
        return OCRResponse(
            results=ocr_results,
            total_detections=len(ocr_results),
            processing_time_ms=round(processing_time, 2),
            image_size=image_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing base64 image: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "model_loaded": ocr_model is not None,
        "gpu_info": {
            "gpu_available": paddle.is_compiled_with_cuda(),
            "current_device": str(paddle.get_device()),
            "device_count": paddle.device.cuda.device_count() if paddle.is_compiled_with_cuda() else 0
        },
        "supported_languages": ["en", "ch", "ta", "te", "ka", "ja", "ko"],  # Common languages
        "api_version": "1.0.0"
    }

if __name__ == "__main__":
    print("Starting PaddleOCR API Server...")
    print("Make sure you have:")
    print("1. PaddleOCR installed: pip install paddlepaddle-gpu paddleocr")
    print("2. GPU drivers and CUDA properly configured")
    print("3. Required packages: pip install fastapi uvicorn python-multipart pillow")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,  # Different port from your medgemma API
        log_level="info"
    )
