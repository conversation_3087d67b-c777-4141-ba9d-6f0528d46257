import os
import io
import base64
import traceback
from typing import List, Optional, Dict, Any
import uvicorn
import paddle
from fastapi import FastAP<PERSON>, HTTPException, File, UploadFile, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image
import numpy as np
from paddleocr import PaddleOCR

# Initialize FastAPI app
app = FastAPI(
    title="PaddleOCR API",
    description="Fast OCR inference API using PaddleOCR with GPU support",
    version="1.0.0"
)

# Global OCR model
ocr_model = None

# Pydantic models for request/response
class OCRResult(BaseModel):
    text: str
    confidence: float
    bbox: List[List[float]]  # Bounding box coordinates [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

class OCRResponse(BaseModel):
    results: List[OCRResult]
    total_detections: int
    processing_time_ms: float
    image_size: Dict[str, int]  # {"width": w, "height": h}

class OCRConfig(BaseModel):
    use_angle_cls: Optional[bool] = True
    use_dilation: Optional[bool] = False
    text_score: Optional[float] = 0.5

@app.on_event("startup")
async def load_ocr_model():
    """Load the PaddleOCR model on startup with GPU support"""
    global ocr_model
    
    try:
        print("Setting up GPU device...")
        # Set GPU device
        paddle.set_device('gpu')
        print(f"GPU available: {paddle.is_compiled_with_cuda()}")
        
        print("Loading PaddleOCR model...")
        # Initialize PaddleOCR with optimized settings for speed
        ocr_model = PaddleOCR(
            use_angle_cls=True,  # Enable text angle classification for better accuracy
            lang='en',  # Default to English, can be changed
            use_gpu=True,  # Enable GPU acceleration
            show_log=False,  # Disable verbose logging for production
            # Disable some features for faster inference
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )
        print("PaddleOCR model loaded successfully!")
        
    except Exception as e:
        print(f"Error loading PaddleOCR model: {str(e)}")
        print(traceback.format_exc())
        raise e

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "PaddleOCR API is running",
        "status": "healthy",
        "model_loaded": ocr_model is not None,
        "gpu_available": paddle.is_compiled_with_cuda(),
        "paddle_device": str(paddle.get_device())
    }

@app.post("/ocr", response_model=OCRResponse)
async def extract_text(
    file: UploadFile = File(...),
    config: str = Form(default='{"use_angle_cls": true, "text_score": 0.5}')
):
    """
    Extract text from an uploaded image with confidence scores and bounding boxes
    
    Args:
        file: Image file (PNG, JPG, JPEG, etc.)
        config: JSON string with OCR configuration options
    
    Returns:
        OCRResponse with extracted text, confidence scores, and bounding boxes
    """
    if ocr_model is None:
        raise HTTPException(status_code=503, detail="OCR model not loaded")
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        import time
        start_time = time.time()
        
        # Read and process the image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        image_size = {"width": image.width, "height": image.height}
        
        # Convert PIL image to numpy array for PaddleOCR
        image_np = np.array(image)
        
        # Parse configuration
        try:
            import json
            ocr_config = json.loads(config)
        except json.JSONDecodeError:
            ocr_config = {"use_angle_cls": True, "text_score": 0.5}
        
        # Run OCR inference
        results = ocr_model.ocr(image_np, cls=ocr_config.get("use_angle_cls", True))
        
        # Process results
        ocr_results = []
        if results and results[0]:  # Check if results exist and are not empty
            for line in results[0]:
                if line:  # Check if line is not None
                    bbox = line[0]  # Bounding box coordinates
                    text_info = line[1]  # (text, confidence)
                    
                    if text_info and len(text_info) == 2:
                        text, confidence = text_info
                        
                        # Filter by confidence threshold
                        if confidence >= ocr_config.get("text_score", 0.5):
                            ocr_results.append(OCRResult(
                                text=text.strip(),
                                confidence=float(confidence),
                                bbox=[[float(coord) for coord in point] for point in bbox]
                            ))
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        return OCRResponse(
            results=ocr_results,
            total_detections=len(ocr_results),
            processing_time_ms=round(processing_time, 2),
            image_size=image_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

@app.post("/ocr-base64", response_model=OCRResponse)
async def extract_text_base64(
    image_base64: str = Form(...),
    config: str = Form(default='{"use_angle_cls": true, "text_score": 0.5}')
):
    """
    Extract text from a base64-encoded image
    
    Args:
        image_base64: Base64-encoded image string
        config: JSON string with OCR configuration options
    
    Returns:
        OCRResponse with extracted text, confidence scores, and bounding boxes
    """
    if ocr_model is None:
        raise HTTPException(status_code=503, detail="OCR model not loaded")
    
    try:
        # Decode base64 image
        if image_base64.startswith('data:image'):
            # Remove data URL prefix if present
            image_base64 = image_base64.split(',')[1]
        
        image_data = base64.b64decode(image_base64)
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Get image dimensions
        image_size = {"width": image.width, "height": image.height}
        
        # Convert PIL image to numpy array for PaddleOCR
        image_np = np.array(image)
        
        # Parse configuration
        try:
            import json
            ocr_config = json.loads(config)
        except json.JSONDecodeError:
            ocr_config = {"use_angle_cls": True, "text_score": 0.5}
        
        import time
        start_time = time.time()
        
        # Run OCR inference
        results = ocr_model.ocr(image_np, cls=ocr_config.get("use_angle_cls", True))
        
        # Process results (same logic as above)
        ocr_results = []
        if results and results[0]:
            for line in results[0]:
                if line:
                    bbox = line[0]
                    text_info = line[1]
                    
                    if text_info and len(text_info) == 2:
                        text, confidence = text_info
                        
                        if confidence >= ocr_config.get("text_score", 0.5):
                            ocr_results.append(OCRResult(
                                text=text.strip(),
                                confidence=float(confidence),
                                bbox=[[float(coord) for coord in point] for point in bbox]
                            ))
        
        processing_time = (time.time() - start_time) * 1000
        
        return OCRResponse(
            results=ocr_results,
            total_detections=len(ocr_results),
            processing_time_ms=round(processing_time, 2),
            image_size=image_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing base64 image: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "model_loaded": ocr_model is not None,
        "gpu_info": {
            "gpu_available": paddle.is_compiled_with_cuda(),
            "current_device": str(paddle.get_device()),
            "device_count": paddle.device.cuda.device_count() if paddle.is_compiled_with_cuda() else 0
        },
        "supported_languages": ["en", "ch", "ta", "te", "ka", "ja", "ko"],  # Common languages
        "api_version": "1.0.0"
    }

if __name__ == "__main__":
    print("Starting PaddleOCR API Server...")
    print("Make sure you have:")
    print("1. PaddleOCR installed: pip install paddlepaddle-gpu paddleocr")
    print("2. GPU drivers and CUDA properly configured")
    print("3. Required packages: pip install fastapi uvicorn python-multipart pillow")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,  # Different port from your medgemma API
        log_level="info"
    )
