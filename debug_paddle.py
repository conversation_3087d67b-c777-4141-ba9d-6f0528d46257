from paddleocr import PaddleOCR
import paddle
import numpy as np
from PIL import Image

# Set GPU device
paddle.set_device('gpu')

# Initialize PaddleOCR exactly like in the API
ocr_model = PaddleOCR(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
    use_textline_orientation=False
)

# Load and process the image exactly like in the API
image = Image.open("download.jpg")
if image.mode != 'RGB':
    image = image.convert('RGB')

image_np = np.array(image)

print("Testing predict method...")
results = ocr_model.predict(image_np)

print(f"Results type: {type(results)}")
print(f"Results: {results}")

if hasattr(results, 'res'):
    print("Has .res attribute")
    res = results.res
    print(f"res keys: {res.keys() if isinstance(res, dict) else 'Not a dict'}")
    if isinstance(res, dict):
        if 'rec_texts' in res:
            print(f"rec_texts: {res['rec_texts']}")
        if 'rec_scores' in res:
            print(f"rec_scores: {res['rec_scores']}")
else:
    print("No .res attribute")
    if isinstance(results, list):
        print(f"List length: {len(results)}")
        if len(results) > 0:
            print(f"First element type: {type(results[0])}")
            print(f"First element: {results[0]}")
