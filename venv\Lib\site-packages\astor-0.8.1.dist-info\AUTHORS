Original author of astor/codegen.py:
* <PERSON><PERSON> <<EMAIL>>

And with some modifications based on <PERSON><PERSON>'s code:
* <PERSON> <<EMAIL>>

* <PERSON><PERSON><PERSON>eksag <<EMAIL>>
* <PERSON> <<EMAIL>>
* A<PERSON><PERSON><PERSON><PERSON> L <<EMAIL>>
* <PERSON> <<EMAIL>>
* Whyzgeek <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
* <PERSON><PERSON> <*************>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON><PERSON> <<EMAIL>>
