{"input_path": "download.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[42, 22], [79, 22], [79, 36], [42, 36]], [[111, 23], [158, 23], [158, 34], [111, 34]], [[44, 34], [96, 34], [96, 44], [44, 44]], [[23, 48], [46, 48], [46, 56], [23, 56]], [[53, 46], [121, 46], [121, 56], [53, 56]], [[21, 59], [105, 58], [105, 69], [21, 70]], [[146, 60], [186, 60], [186, 70], [146, 70]], [[216, 55], [248, 58], [247, 71], [214, 68]], [[23, 76], [74, 76], [74, 85], [23, 85]], [[24, 71], [56, 71], [56, 78], [24, 78]], [[134, 72], [213, 72], [213, 81], [134, 81]], [[22, 89], [71, 89], [71, 99], [22, 99]], [[23, 82], [71, 82], [71, 92], [23, 92]], [[134, 80], [213, 80], [213, 90], [134, 90]], [[23, 96], [71, 96], [71, 107], [23, 107]], [[196, 93], [247, 93], [247, 103], [196, 103]], [[22, 108], [52, 108], [52, 118], [22, 118]], [[66, 109], [90, 109], [90, 117], [66, 117]], [[166, 104], [213, 104], [213, 113], [166, 113]], [[21, 116], [57, 114], [57, 125], [22, 127]], [[63, 117], [91, 114], [92, 125], [64, 127]], [[165, 117], [222, 116], [222, 127], [165, 128]], [[167, 112], [209, 112], [209, 119], [167, 119]], [[22, 136], [36, 136], [36, 144], [22, 144]], [[56, 141], [96, 142], [96, 152], [56, 151]], [[57, 136], [97, 136], [97, 145], [57, 145]], [[113, 136], [153, 136], [153, 145], [113, 145]], [[114, 132], [142, 132], [142, 139], [114, 139]], [[22, 143], [39, 143], [39, 151], [22, 151]], [[115, 143], [152, 143], [152, 150], [115, 150]], [[22, 156], [36, 156], [36, 165], [22, 165]], [[133, 155], [247, 155], [247, 165], [133, 165]], [[139, 149], [248, 149], [248, 159], [139, 159]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.0, "rec_texts": ["United", "OptumRx", "Healthcare", "Merber:", "SUBSCRIBER SMITH", "Moner ID 123456789", "Group Nuroer", "98765", "SPOUSE SMITH", "Dependents", "Custoner Literal Name Line!", "CHILD2 SMITH", "CHILD1 SMITH", "CustonatLteraltNameLine2", "CHILD3 SMITH", "Payer ID 87726", "Office:$20", "ER:$300", "Rx Bc 610279", "UgCore:$7", "Spec: 530", "<PERSON><PERSON>", "RxPCN9999", "INN", "**********", "$99999/569999", "$86999/599699", "COPUMIFM", "OON", "590000/506600", "0501", "AyAp", "UnitedHealthcare Choice Plus"], "rec_scores": [0.9964198470115662, 0.9578993916511536, 0.9846154451370239, 0.7589351534843445, 0.9510831832885742, 0.8014283180236816, 0.6558229327201843, 0.9883588552474976, 0.9573261141777039, 0.7532230019569397, 0.8424778580665588, 0.9560441970825195, 0.9508388042449951, 0.7071852087974548, 0.9558406472206116, 0.9411129355430603, 0.738991379737854, 0.8749263882637024, 0.7846412658691406, 0.6796867847442627, 0.6444798707962036, 0.8137890696525574, 0.885770857334137, 0.6763157844543457, 0.48698800802230835, 0.684341311454773, 0.4646585285663605, 0.6156584024429321, 0.8553007245063782, 0.5163953304290771, 0.6580896973609924, 0.670323371887207, 0.921635627746582], "rec_polys": [[[42, 22], [79, 22], [79, 36], [42, 36]], [[111, 23], [158, 23], [158, 34], [111, 34]], [[44, 34], [96, 34], [96, 44], [44, 44]], [[23, 48], [46, 48], [46, 56], [23, 56]], [[53, 46], [121, 46], [121, 56], [53, 56]], [[21, 59], [105, 58], [105, 69], [21, 70]], [[146, 60], [186, 60], [186, 70], [146, 70]], [[216, 55], [248, 58], [247, 71], [214, 68]], [[23, 76], [74, 76], [74, 85], [23, 85]], [[24, 71], [56, 71], [56, 78], [24, 78]], [[134, 72], [213, 72], [213, 81], [134, 81]], [[22, 89], [71, 89], [71, 99], [22, 99]], [[23, 82], [71, 82], [71, 92], [23, 92]], [[134, 80], [213, 80], [213, 90], [134, 90]], [[23, 96], [71, 96], [71, 107], [23, 107]], [[196, 93], [247, 93], [247, 103], [196, 103]], [[22, 108], [52, 108], [52, 118], [22, 118]], [[66, 109], [90, 109], [90, 117], [66, 117]], [[166, 104], [213, 104], [213, 113], [166, 113]], [[21, 116], [57, 114], [57, 125], [22, 127]], [[63, 117], [91, 114], [92, 125], [64, 127]], [[165, 117], [222, 116], [222, 127], [165, 128]], [[167, 112], [209, 112], [209, 119], [167, 119]], [[22, 136], [36, 136], [36, 144], [22, 144]], [[56, 141], [96, 142], [96, 152], [56, 151]], [[57, 136], [97, 136], [97, 145], [57, 145]], [[113, 136], [153, 136], [153, 145], [113, 145]], [[114, 132], [142, 132], [142, 139], [114, 139]], [[22, 143], [39, 143], [39, 151], [22, 151]], [[115, 143], [152, 143], [152, 150], [115, 150]], [[22, 156], [36, 156], [36, 165], [22, 165]], [[133, 155], [247, 155], [247, 165], [133, 165]], [[139, 149], [248, 149], [248, 159], [139, 159]]], "rec_boxes": [[42, 22, 79, 36], [111, 23, 158, 34], [44, 34, 96, 44], [23, 48, 46, 56], [53, 46, 121, 56], [21, 58, 105, 70], [146, 60, 186, 70], [214, 55, 248, 71], [23, 76, 74, 85], [24, 71, 56, 78], [134, 72, 213, 81], [22, 89, 71, 99], [23, 82, 71, 92], [134, 80, 213, 90], [23, 96, 71, 107], [196, 93, 247, 103], [22, 108, 52, 118], [66, 109, 90, 117], [166, 104, 213, 113], [21, 114, 57, 127], [63, 114, 92, 127], [165, 116, 222, 128], [167, 112, 209, 119], [22, 136, 36, 144], [56, 141, 96, 152], [57, 136, 97, 145], [113, 136, 153, 145], [114, 132, 142, 139], [22, 143, 39, 151], [115, 143, 152, 150], [22, 156, 36, 165], [133, 155, 247, 165], [139, 149, 248, 159]]}