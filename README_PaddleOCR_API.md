# PaddleOCR FastAPI Service

A high-performance FastAPI service for OCR (Optical Character Recognition) using PaddleOCR with GPU acceleration.

## Features

- **GPU Acceleration**: Utilizes GPU for fast inference
- **High Accuracy**: Returns text with confidence scores
- **Bounding Boxes**: Provides precise text location coordinates
- **Multiple Input Formats**: Supports file upload and base64 encoded images
- **Configurable**: Adjustable confidence thresholds and processing options
- **Production Ready**: Built with FastAPI for scalability and performance

## Installation

1. **Install dependencies:**
```bash
pip install -r requirements_paddleocr.txt
```

2. **For GPU support, ensure you have CUDA installed and compatible GPU drivers**

## Usage

### Starting the Server

```bash
python paddleocr_api.py
```

The server will start on `http://localhost:8001`

### API Endpoints

#### 1. Health Check
```bash
GET /health
```

#### 2. OCR with File Upload
```bash
POST /ocr
```

**Parameters:**
- `file`: Image file (multipart/form-data)
- `config`: JSON string with configuration options

**Example using curl:**
```bash
curl -X POST "http://localhost:8001/ocr" \
  -F "file=@your_image.jpg" \
  -F 'config={"use_angle_cls": true, "text_score": 0.5}'
```

#### 3. OCR with Base64 Image
```bash
POST /ocr-base64
```

**Parameters:**
- `image_base64`: Base64 encoded image string
- `config`: JSON string with configuration options

### Response Format

```json
{
  "results": [
    {
      "text": "Extracted text",
      "confidence": 0.95,
      "bbox": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
    }
  ],
  "total_detections": 1,
  "processing_time_ms": 150.5,
  "image_size": {"width": 800, "height": 600}
}
```

### Configuration Options

- `use_angle_cls`: Enable text angle classification (default: true)
- `text_score`: Confidence threshold for text detection (default: 0.5)
- `use_dilation`: Enable text dilation (default: false)

## Testing

Run the test client:
```bash
python test_paddleocr_api.py
```

## Performance Tips

1. **GPU Memory**: Ensure sufficient GPU memory for large images
2. **Batch Processing**: For multiple images, send requests concurrently
3. **Image Preprocessing**: Resize very large images for faster processing
4. **Confidence Threshold**: Adjust `text_score` based on your accuracy requirements

## Supported Image Formats

- PNG
- JPEG/JPG
- BMP
- TIFF
- WebP

## Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `400`: Bad request (invalid image format)
- `500`: Internal server error
- `503`: Service unavailable (model not loaded)

## Integration Examples

### Python Client
```python
import requests

# File upload
with open('image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8001/ocr',
        files={'file': f},
        data={'config': '{"text_score": 0.7}'}
    )
    result = response.json()
```

### JavaScript/Node.js
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('config', JSON.stringify({text_score: 0.5}));

fetch('http://localhost:8001/ocr', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

## Deployment

For production deployment:

1. **Use a production ASGI server:**
```bash
pip install gunicorn
gunicorn paddleocr_api:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8001
```

2. **Configure reverse proxy (nginx):**
```nginx
location /ocr {
    proxy_pass http://localhost:8001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    client_max_body_size 10M;
}
```

3. **Environment variables:**
```bash
export PADDLE_DEVICE=gpu
export CUDA_VISIBLE_DEVICES=0
```

## Troubleshooting

1. **GPU not detected**: Check CUDA installation and GPU drivers
2. **Out of memory**: Reduce image size or batch size
3. **Slow inference**: Ensure GPU is being used (`paddle.get_device()`)
4. **Model loading errors**: Check PaddleOCR installation and dependencies

## License

This project uses PaddleOCR which is licensed under Apache 2.0 License.
