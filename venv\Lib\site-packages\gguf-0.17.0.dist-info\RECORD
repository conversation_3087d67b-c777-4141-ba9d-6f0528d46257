../../Scripts/gguf-convert-endian.exe,sha256=3-2tvz7wRRS1sCK7_HHSh5znHUc90A68uNrw_Hu_VM0,108429
../../Scripts/gguf-dump.exe,sha256=AGLBpXRllqLS3njd6MWN5oC5nKGWjt_hnEmPbEGGpq4,108419
../../Scripts/gguf-editor-gui.exe,sha256=Ymr4Lq6QEf2mVWOAfhwHAJ0KboDBbmv2qHfOA77b5do,108425
../../Scripts/gguf-new-metadata.exe,sha256=Ou0iAut_oGPq5EDFAfd6J0XK1jJFQUlJBs-xxZp05Y4,108427
../../Scripts/gguf-set-metadata.exe,sha256=Cevr5bcSwDEHSpBXp45Ec8tl21HWtcb6JpzEuWBdCDk,108427
gguf-0.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gguf-0.17.0.dist-info/LICENSE,sha256=73jH5mWeNMeYGU8NNE6AfHIt5wy8oTWe9UdyZh4Ryjg,1072
gguf-0.17.0.dist-info/METADATA,sha256=Iz-mBnu3FYWz-FSrBlG17K_1gdwjTqg9_dhOfgdDVu4,4396
gguf-0.17.0.dist-info/RECORD,,
gguf-0.17.0.dist-info/WHEEL,sha256=b4K_helf-jlQoXBBETfwnf4B04YC67LOev0jo4fX5m8,88
gguf-0.17.0.dist-info/entry_points.txt,sha256=TozYSFmMxpOaKE3brn9nWp-QkgM_sZ3a_uFDOXUCYig,273
gguf/__init__.py,sha256=PM_AEEzX6ojGAodDt78_LIm19HRCXeA6IXpgcjINfC8,219
gguf/__pycache__/__init__.cpython-312.pyc,,
gguf/__pycache__/constants.cpython-312.pyc,,
gguf/__pycache__/gguf.cpython-312.pyc,,
gguf/__pycache__/gguf_reader.cpython-312.pyc,,
gguf/__pycache__/gguf_writer.cpython-312.pyc,,
gguf/__pycache__/lazy.cpython-312.pyc,,
gguf/__pycache__/metadata.cpython-312.pyc,,
gguf/__pycache__/quants.cpython-312.pyc,,
gguf/__pycache__/tensor_mapping.cpython-312.pyc,,
gguf/__pycache__/utility.cpython-312.pyc,,
gguf/__pycache__/vocab.cpython-312.pyc,,
gguf/constants.py,sha256=GPnafgYEFnL5a33YB1G724kNF2ILTpNc6R9YfOHyhm8,87723
gguf/gguf.py,sha256=8MDu7a0JEXhLUv_tjhYqDrWubVNc41cFvBYZbkZZenI,478
gguf/gguf_reader.py,sha256=6uI4vaLeRC2MJV-uUjgsRoUZ-Rdszi8mE_bEPwnk6QE,14828
gguf/gguf_writer.py,sha256=ukY15lv65d1hwiiKenp-ODQXi14FgIlUPWYDqljeOJ0,43716
gguf/lazy.py,sha256=Axy_plbHoC34nMeK_ORbbxEyEFm-Fz8BxFIeqeipSYA,9214
gguf/metadata.py,sha256=v0kxoYfCk_yXZSnzlSO9AhQ6z_HOUpzs0c6ZV4BRSbA,33301
gguf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gguf/quants.py,sha256=BLtCCqhHBtabaYaAp7EckyYOR5idmoKWhu_Hy3glotk,60771
gguf/scripts/__pycache__/gguf_convert_endian.cpython-312.pyc,,
gguf/scripts/__pycache__/gguf_dump.cpython-312.pyc,,
gguf/scripts/__pycache__/gguf_editor_gui.cpython-312.pyc,,
gguf/scripts/__pycache__/gguf_hash.cpython-312.pyc,,
gguf/scripts/__pycache__/gguf_new_metadata.cpython-312.pyc,,
gguf/scripts/__pycache__/gguf_set_metadata.cpython-312.pyc,,
gguf/scripts/gguf_convert_endian.py,sha256=yzl_MAQ3jyn_9MmOWV1CksHqlohd7DmrG7REwas0rlo,7365
gguf/scripts/gguf_dump.py,sha256=zDgZSSQLyO3S4YJsSUzdebDfwmdqQPN7_VtFZ5BkGAk,21785
gguf/scripts/gguf_editor_gui.py,sha256=frdErSIB90N-sAvqUpbLfdDsaUGMMOWQ-0iumwzzm_M,64398
gguf/scripts/gguf_hash.py,sha256=nyd8kzjRKnOFek5UaD19pNXeAVMXUfFEASZ8konkGX8,3725
gguf/scripts/gguf_new_metadata.py,sha256=U_v5FgbH292x7bsi2dG4rbQcWc14nmAtZEWdLnbkIZs,9767
gguf/scripts/gguf_set_metadata.py,sha256=yGEqcQlCimd-pVl23V7u1giJNN3vfvASRqW8em5YWzs,4145
gguf/tensor_mapping.py,sha256=TC55Q8apmT2yaLqhHTKTd1AXdLOM_nTtM08YhAWtmZY,54193
gguf/utility.py,sha256=80rZ3MdGZ6bX0_yFvLoPTTOlxga8THcihF0q38y5h6M,10808
gguf/vocab.py,sha256=RyrENDagj2fBJ6t6_rRS1_TW4PyRUTNHr7TNRPc1S6w,20616
