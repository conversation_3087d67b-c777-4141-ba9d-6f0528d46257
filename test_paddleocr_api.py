import requests
import json
import base64
from PIL import Image
import io

# API base URL
API_BASE_URL = "http://localhost:8001"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{API_BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_ocr_with_file(image_path: str):
    """Test OCR with file upload"""
    print(f"Testing OCR with file: {image_path}")
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (image_path, f, 'image/jpeg')}
            data = {
                'config': json.dumps({
                    "use_angle_cls": True,
                    "text_score": 0.5
                })
            }
            
            response = requests.post(f"{API_BASE_URL}/ocr", files=files, data=data)
            
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Processing time: {result['processing_time_ms']} ms")
            print(f"Total detections: {result['total_detections']}")
            print(f"Image size: {result['image_size']}")
            print("\nExtracted text:")
            for i, ocr_result in enumerate(result['results']):
                print(f"  {i+1}. Text: '{ocr_result['text']}'")
                print(f"      Confidence: {ocr_result['confidence']:.3f}")
                print(f"      Bbox: {ocr_result['bbox']}")
        else:
            print(f"Error: {response.text}")
            
    except FileNotFoundError:
        print(f"File not found: {image_path}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    print("-" * 50)

def test_ocr_with_base64(image_path: str):
    """Test OCR with base64 encoded image"""
    print(f"Testing OCR with base64: {image_path}")
    
    try:
        # Convert image to base64
        with open(image_path, 'rb') as f:
            image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        data = {
            'image_base64': image_base64,
            'config': json.dumps({
                "use_angle_cls": True,
                "text_score": 0.3  # Lower threshold for testing
            })
        }
        
        response = requests.post(f"{API_BASE_URL}/ocr-base64", data=data)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Processing time: {result['processing_time_ms']} ms")
            print(f"Total detections: {result['total_detections']}")
            print("\nExtracted text:")
            for i, ocr_result in enumerate(result['results']):
                print(f"  {i+1}. Text: '{ocr_result['text']}'")
                print(f"      Confidence: {ocr_result['confidence']:.3f}")
        else:
            print(f"Error: {response.text}")
            
    except FileNotFoundError:
        print(f"File not found: {image_path}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    print("-" * 50)

def create_sample_image():
    """Create a simple test image with text"""
    from PIL import Image, ImageDraw, ImageFont
    
    # Create a simple image with text
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Add some text
    draw.text((50, 50), "Hello, World!", fill='black', font=font)
    draw.text((50, 100), "PaddleOCR Test", fill='black', font=font)
    draw.text((50, 150), "Confidence: 0.95", fill='black', font=font)
    
    # Save the image
    img.save("test_image.png")
    print("Created test_image.png")
    return "test_image.png"

if __name__ == "__main__":
    print("PaddleOCR API Test Client")
    print("=" * 50)
    
    # Test health check
    test_health_check()
    
    # Check if test image exists, create if not
    import os
    test_image = "download.jpg"  # Your existing test image
    if not os.path.exists(test_image):
        print(f"Test image {test_image} not found, creating a sample image...")
        test_image = create_sample_image()
    
    # Test OCR with file upload
    test_ocr_with_file(test_image)
    
    # Test OCR with base64
    test_ocr_with_base64(test_image)
    
    print("Testing complete!")
    print("\nAPI Endpoints:")
    print(f"- Health check: GET {API_BASE_URL}/health")
    print(f"- OCR (file upload): POST {API_BASE_URL}/ocr")
    print(f"- OCR (base64): POST {API_BASE_URL}/ocr-base64")
