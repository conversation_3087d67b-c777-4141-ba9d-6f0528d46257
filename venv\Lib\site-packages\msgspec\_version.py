
# This file was generated by 'versioneer.py' (0.19) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-12-27T11:06:58-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "dd965dce22e5278d4935bea923441ecde31b5325",
 "version": "0.19.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
