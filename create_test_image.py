from PIL import Image, ImageDraw, ImageFont
import requests
import json

def create_test_image():
    """Create a simple test image with clear text"""
    # Create a white background image
    img = Image.new('RGB', (600, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a larger font
    try:
        font_large = ImageFont.truetype("arial.ttf", 36)
        font_medium = ImageFont.truetype("arial.ttf", 24)
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
    
    # Add clear, readable text
    draw.text((50, 50), "HELLO WORLD", fill='black', font=font_large)
    draw.text((50, 120), "PaddleOCR Test Image", fill='black', font=font_medium)
    draw.text((50, 170), "Confidence Score: 0.95", fill='black', font=font_medium)
    draw.text((50, 220), "GPU Acceleration: ON", fill='black', font=font_medium)
    draw.text((50, 270), "FastAPI Service", fill='blue', font=font_medium)
    draw.text((50, 320), "Text Detection Working!", fill='green', font=font_medium)
    
    # Save the image
    img.save("clear_test_image.png")
    print("Created clear_test_image.png")
    return "clear_test_image.png"

def test_ocr_with_clear_image():
    """Test OCR with the clear test image"""
    image_path = create_test_image()
    
    print(f"\nTesting OCR with clear image: {image_path}")
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (image_path, f, 'image/png')}
            data = {
                'config': json.dumps({
                    "text_score": 0.3  # Lower threshold to catch more text
                })
            }
            
            response = requests.post("http://localhost:8001/ocr", files=files, data=data)
            
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Processing time: {result['processing_time_ms']} ms")
            print(f"Total detections: {result['total_detections']}")
            print(f"Image size: {result['image_size']}")
            print("\nExtracted text:")
            for i, ocr_result in enumerate(result['results']):
                print(f"  {i+1}. Text: '{ocr_result['text']}'")
                print(f"      Confidence: {ocr_result['confidence']:.3f}")
                print(f"      Bbox: {ocr_result['bbox']}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_ocr_with_clear_image()
