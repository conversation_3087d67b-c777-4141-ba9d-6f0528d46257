../../Scripts/get_gprof,sha256=j9UcL-zziVCpFrOmU5UpTmbk_bAx98arlnUvMaTkFzQ,2502
../../Scripts/get_objgraph,sha256=k6e_Hw_ZB65EjXEBvEjHEKVyJtfwkkx6MrCdhf2y5mg,1696
../../Scripts/undill,sha256=EcyVyURVaNw7-5J5Mpo4fr0vCEyOTSdlLWxB4EIJYqY,632
dill-0.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dill-0.4.0.dist-info/LICENSE,sha256=mYpIzbuubSrNbyOVUajkpFDpZ-udj9jiQsNOnmTkDiY,1790
dill-0.4.0.dist-info/METADATA,sha256=Zzr1eKtuTvrriL-H_4DmN7fxjB9tENzGRz0DwAI7u5Q,10174
dill-0.4.0.dist-info/RECORD,,
dill-0.4.0.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
dill-0.4.0.dist-info/top_level.txt,sha256=HLSIyYIjQzJiBvs3_-16ntezE3j6mWGTW0DT1xDd7X0,5
dill/__diff.py,sha256=yLCKcd0GkDxR86wdE0SG2qw5Yxhon6qHeOeI1li8Vrc,7146
dill/__info__.py,sha256=sD2tcjIAuRmMAIqBtF4-JWK7VTCIoQx3axUNBQb-qPw,10756
dill/__init__.py,sha256=u5X9cMJo3zAtpGN4MUATU71s5wNvnIbXsZXi29DyvXE,3798
dill/__pycache__/__diff.cpython-312.pyc,,
dill/__pycache__/__info__.cpython-312.pyc,,
dill/__pycache__/__init__.cpython-312.pyc,,
dill/__pycache__/_dill.cpython-312.pyc,,
dill/__pycache__/_objects.cpython-312.pyc,,
dill/__pycache__/_shims.cpython-312.pyc,,
dill/__pycache__/detect.cpython-312.pyc,,
dill/__pycache__/logger.cpython-312.pyc,,
dill/__pycache__/objtypes.cpython-312.pyc,,
dill/__pycache__/pointers.cpython-312.pyc,,
dill/__pycache__/session.cpython-312.pyc,,
dill/__pycache__/settings.cpython-312.pyc,,
dill/__pycache__/source.cpython-312.pyc,,
dill/__pycache__/temp.cpython-312.pyc,,
dill/_dill.py,sha256=DX51DqW1DXL_5VdZ-zF5w9avxiVOsNh6cNwMkd39Fwo,91313
dill/_objects.py,sha256=wPETr0_Gtj__cFAIEO6FRSC_vpBYexmlDiHl_d8geJU,19740
dill/_shims.py,sha256=mGN22u7TeK-keDvhGvWZVvCm-GW0TY04MXW-Hncbh7c,6635
dill/detect.py,sha256=L0eWBEVzqMV6nf8gd5R4G-cytzJEjBNTKAlsVVkJRlY,11207
dill/logger.py,sha256=WNX59lxDHuZnSMKZ294i0uCDTmbneWPLu79Be7mOa-Q,11143
dill/objtypes.py,sha256=NvSqfHX6AY2-QOVUv0hEda_GEcx_uiuGPQvILJu8TyY,736
dill/pointers.py,sha256=Q8AYqhdcvdq9X4HZ46lihM2bnZqGZG2vVoGpRxTHRNE,4467
dill/session.py,sha256=qc41kfzXT9MIx1KGSEIVbUsDKBHErhptpeQzwcGspkU,23541
dill/settings.py,sha256=hmtpAbplWE-HHxRHLPEkOxpEqWoCMTMn_FRa-1seao4,630
dill/source.py,sha256=aaK9d3J7yNQuVw15ESRXxVqE8DxYHf1TfSOBEQYcZmU,45507
dill/temp.py,sha256=QhkBKO5eNsEwSHdhiw5nCzALchAsl1yWQ1apZlVXY7w,8027
dill/tests/__init__.py,sha256=5z3npvfFh6Xw5xa8ML-AQk3PDyTc3MIngJlUVVf35vk,479
dill/tests/__main__.py,sha256=i9A86Q2NGoEhO-080sgnv2-TXxTRoH-gN2RTaPvpGvA,899
dill/tests/__pycache__/__init__.cpython-312.pyc,,
dill/tests/__pycache__/__main__.cpython-312.pyc,,
dill/tests/__pycache__/test_abc.cpython-312.pyc,,
dill/tests/__pycache__/test_check.cpython-312.pyc,,
dill/tests/__pycache__/test_classdef.cpython-312.pyc,,
dill/tests/__pycache__/test_dataclasses.cpython-312.pyc,,
dill/tests/__pycache__/test_detect.cpython-312.pyc,,
dill/tests/__pycache__/test_dictviews.cpython-312.pyc,,
dill/tests/__pycache__/test_diff.cpython-312.pyc,,
dill/tests/__pycache__/test_extendpickle.cpython-312.pyc,,
dill/tests/__pycache__/test_fglobals.cpython-312.pyc,,
dill/tests/__pycache__/test_file.cpython-312.pyc,,
dill/tests/__pycache__/test_functions.cpython-312.pyc,,
dill/tests/__pycache__/test_functors.cpython-312.pyc,,
dill/tests/__pycache__/test_logger.cpython-312.pyc,,
dill/tests/__pycache__/test_mixins.cpython-312.pyc,,
dill/tests/__pycache__/test_module.cpython-312.pyc,,
dill/tests/__pycache__/test_moduledict.cpython-312.pyc,,
dill/tests/__pycache__/test_nested.cpython-312.pyc,,
dill/tests/__pycache__/test_objects.cpython-312.pyc,,
dill/tests/__pycache__/test_properties.cpython-312.pyc,,
dill/tests/__pycache__/test_pycapsule.cpython-312.pyc,,
dill/tests/__pycache__/test_recursive.cpython-312.pyc,,
dill/tests/__pycache__/test_registered.cpython-312.pyc,,
dill/tests/__pycache__/test_restricted.cpython-312.pyc,,
dill/tests/__pycache__/test_selected.cpython-312.pyc,,
dill/tests/__pycache__/test_session.cpython-312.pyc,,
dill/tests/__pycache__/test_source.cpython-312.pyc,,
dill/tests/__pycache__/test_sources.cpython-312.pyc,,
dill/tests/__pycache__/test_temp.cpython-312.pyc,,
dill/tests/__pycache__/test_threads.cpython-312.pyc,,
dill/tests/__pycache__/test_weakref.cpython-312.pyc,,
dill/tests/test_abc.py,sha256=OT50obWxTiqk2eZLrbfHGPU9_Wr30wFTQ1Z8CfsdClU,4227
dill/tests/test_check.py,sha256=OT6iu8R1BzZFQdiR3YgPoKKV7KJ2AYLJP62zTgsThdo,1396
dill/tests/test_classdef.py,sha256=M3zzqKruuqIFTZDUqKYbjMtJmVkI59N0DxWZe019-qM,8600
dill/tests/test_dataclasses.py,sha256=MPkDH5pfvYlYbwlGm-uFMN7svhPUImqKUYpROWO_Wak,890
dill/tests/test_detect.py,sha256=wjgVFO8UMDLaM7A1cu1bQNXgYQCOpLGYqMYxID5CNsk,4144
dill/tests/test_dictviews.py,sha256=qPAuqmp1yApPsARB6EKjmIWIAigK6P0L2Q68my02D-I,1337
dill/tests/test_diff.py,sha256=TXCpiSPVTwEVR9-cs_sr_MYP9T7R2Pw2N2PAvnOvWFI,2667
dill/tests/test_extendpickle.py,sha256=mcnPB0_YIqx6Ct678LUzWER1FRA1kVSRxE6PKhaN2oQ,1315
dill/tests/test_fglobals.py,sha256=tbdBdLzW_7rx5mR-BzeThzEhH0KbtDZfAazLBYC_h94,1676
dill/tests/test_file.py,sha256=XjW_EFSPsH7udEijchzef-OunBiJGJ-ieLRm8Vcai4Q,13578
dill/tests/test_functions.py,sha256=AVBbHCDffw4Jca3Kijjae4rQfX0ZFTgurUjPeP2qqoM,4267
dill/tests/test_functors.py,sha256=GSHR6UGBuiq0iUk4mk04Ly9ohSVdt9fBCrEYwByzSxk,930
dill/tests/test_logger.py,sha256=PiZHr3bGIh6rW_Rx3uAQlVl5oFl-n2BQjXkpPzbmjng,2385
dill/tests/test_mixins.py,sha256=GHO-GXeYvWzZeysJnsIDR8sTj_RADBs0-z23r_SfoNA,4007
dill/tests/test_module.py,sha256=BOJ1mU4qaK680gEERfFXAIElTTuOg1nY4Ne26UhcpvU,1943
dill/tests/test_moduledict.py,sha256=SlosX5tkmdfGLuhYweASu-WKIsrgphXrEm20veZmFJc,1182
dill/tests/test_nested.py,sha256=dLgI3ZKWWAyZxXfTF131EHtph8yTS-e9Wqb8Mj3HA6A,3146
dill/tests/test_objects.py,sha256=QyYSMiBZDyjhsGwDkdUeJH65rHODQMrLFajy-fUqzLI,1931
dill/tests/test_properties.py,sha256=PinOM_C2Fzfj_3FlQm8OdjAmJ8WQ38nHWKkfHI4rCeA,1346
dill/tests/test_pycapsule.py,sha256=XgT8VQZAAY3q7oV3N8726PnqU4GjuxSoT_FuFsoNsso,1417
dill/tests/test_recursive.py,sha256=Bq2Q--Ro3Mb4CppYm5K5v0__5mGcFvSEjzmZOC81C64,4182
dill/tests/test_registered.py,sha256=jgUBl_msBiqQTgvCC1bwKTGK-jmAz2FSE4pcJgEDRBw,1573
dill/tests/test_restricted.py,sha256=mk93WLtNAEQr03h4N-NZO7Wr9xPWvzgMPOPEdi4Aku4,783
dill/tests/test_selected.py,sha256=LjhZNntQ9KDJf9lhEYTHqzbFpsoeEFK2wobO1JT0FRg,3258
dill/tests/test_session.py,sha256=-xmj46QxC60MaRIljv2eVrqIQYZrIcL54PsPaTLpoXc,10161
dill/tests/test_source.py,sha256=EOTG_Fh0cWlICTRq04KH0Y5_G9BgGigkAOhr3CD1-dk,7059
dill/tests/test_sources.py,sha256=sQXVRsv_u9i1kTyJYRtX_Ykb3TgaPrQ3sI2az8bNxQQ,8672
dill/tests/test_temp.py,sha256=8NvuImVkYM1dw-j_nhaeDAb8Oan9whCOoehXktqurtw,2619
dill/tests/test_threads.py,sha256=PD8RVdtu_kCkM7AVV8a81ywVWRi1g3GyDRocx04ZRFg,1257
dill/tests/test_weakref.py,sha256=TfQHVMqgzeBywBiTjHj6ep8E6GF90y5SFzLgWbRbc2Q,1602
